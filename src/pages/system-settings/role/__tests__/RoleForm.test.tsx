/* eslint-disable @typescript-eslint/no-explicit-any */
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { useForm } from "react-hook-form";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { RoleForm } from "../components/role-form";
import type { RoleData } from "../schemas/role-schemas";

// Mock dependencies
const mockUseRoleMutation = vi.fn();
const mockUsePermissionsQuery = vi.fn();

// Mock the hooks
vi.mock("../hooks/use-role.mutation", () => ({
  useRoleMutation: () => mockUseRoleMutation(),
}));

vi.mock("@/features/permissions/hooks/use-permissions.query", () => ({
  usePermissionsQuery: () => mockUsePermissionsQuery(),
}));

// Store the onSelect callback for direct testing
let mockPermissionsComboboxOnSelect: ((value: string) => void) | null = null;

// Mock the PermissionsCombobox component
vi.mock("../components/permissions-combobox", () => ({
  PermissionsCombobox: ({
    onSelect,
    availablePermissions,
    currentPermissions,
    placeholder,
    emptyText,
  }: {
    onSelect: (value: string) => void;
    availablePermissions: string[];
    currentPermissions: string[];
    placeholder: string;
    emptyText: string;
  }) => {
    // Store the callback for direct testing
    mockPermissionsComboboxOnSelect = onSelect;

    return (
      <div data-testid="permissions-combobox">
        <button
          onClick={() => onSelect("permission:create")}
          data-testid="select-permission"
        >
          {placeholder || "Select a permission"}
        </button>
        <div data-testid="available-permissions">
          {availablePermissions.length > 0
            ? availablePermissions.join(",")
            : emptyText}
        </div>
        <div data-testid="current-permissions">
          {Array.isArray(currentPermissions)
            ? currentPermissions.join(",")
            : ""}
        </div>
      </div>
    );
  },
}));

// Store the onRemovePermission callback for direct testing
let mockPermissionsTableOnRemove: ((permission: string) => void) | null = null;

// Create a mock component that renders based on the current permissions state
const MockPermissionsTable = vi.fn(
  ({
    permissions,
    onRemovePermission,
    isLoading,
    loadingText,
    noPermissionsText,
  }: {
    permissions: string[];
    onRemovePermission: (permission: string) => void;
    isLoading?: boolean;
    loadingText?: string;
    noPermissionsText?: string;
  }) => {
    // Store the callback for direct testing
    mockPermissionsTableOnRemove = onRemovePermission;

    return (
      <div data-testid="permissions-table">
        {isLoading ? (
          <div>Loading permissions...</div>
        ) : !Array.isArray(permissions) || permissions.length === 0 ? (
          <div>No permissions added</div>
        ) : (
          permissions.map((permission) => (
            <div key={permission} data-testid={`permission-${permission}`}>
              {permission}
              <button
                onClick={() => onRemovePermission(permission)}
                aria-label="Remove permission"
                data-testid={`remove-${permission}`}
              >
                Remove
              </button>
            </div>
          ))
        )}
      </div>
    );
  }
);

vi.mock("../components/permissions-table", () => ({
  PermissionsTable: (props: any) => MockPermissionsTable(props),
}));

// Mock react-router
vi.mock("react-router", () => ({
  Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
    <a href={to}>{children}</a>
  ),
}));

// Mock react-hook-form
vi.mock("react-hook-form", () => ({
  ...vi.importActual("react-hook-form"),
  useForm: vi.fn().mockImplementation(({ defaultValues }) => {
    // Create a mutable copy of defaultValues that we can modify
    let formValues = { ...defaultValues };

    const formState = { errors: {} };

    const watch = vi.fn().mockImplementation((field) => {
      if (field) {
        return formValues[field];
      }
      return formValues;
    });

    const getValues = vi.fn().mockImplementation((field) => {
      if (field) {
        return formValues[field];
      }
      return formValues;
    });

    const setValue = vi.fn().mockImplementation((field, value, options) => {
      formValues[field] = value;
      return formValues;
    });

    const handleSubmit = (fn: any) => (e: any) => {
      e?.preventDefault();
      fn(formValues);
    };

    const reset = vi.fn().mockImplementation((newValues) => {
      if (newValues) {
        formValues = { ...newValues };
      } else {
        formValues = { ...defaultValues };
      }
    });

    return {
      formState,
      watch,
      getValues,
      setValue,
      handleSubmit,
      reset,
      control: {},
    };
  }),
  Controller: ({ render }: any) =>
    render({
      field: {
        onChange: vi.fn(),
        onBlur: vi.fn(),
        value: "",
        name: "test-field",
        ref: vi.fn(),
      },
      fieldState: { error: null },
    }),
  // Add FormProvider that's used by the Form component
  FormProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  // Add useFormContext that's used by the form components
  useFormContext: () => ({
    getFieldState: vi.fn().mockReturnValue({}),
    formState: { errors: {} },
    register: vi.fn(),
    handleSubmit: vi.fn(),
    watch: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    trigger: vi.fn(),
    clearErrors: vi.fn(),
    reset: vi.fn(),
    setError: vi.fn(),
    control: { register: vi.fn(), unregister: vi.fn() },
  }),
}));

// Override the Form component to avoid nested form elements
vi.mock("@/components/ui/form", () => ({
  Form: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  FormControl: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  FormField: ({ children, render }: any) => render({ field: {} }),
  FormItem: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  FormLabel: ({ children }: { children: React.ReactNode }) => (
    <label>{children}</label>
  ),
  FormMessage: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      // Remove the namespace prefix for lookup
      const keyWithoutNamespace = key.includes(":") ? key.split(":")[1] : key;

      const translations: Record<string, string> = {
        // With namespace
        "roleManagement:roleName": "Role Name",
        "roleManagement:description": "Description",
        "roleManagement:permissions": "Permissions",
        "roleManagement:addPermission": "Add Permission",
        "roleManagement:selectPermission": "Select a permission",
        "roleManagement:noPermissionsAvailable": "No permissions available",
        "roleManagement:noPermissions": "No permissions added",
        "roleManagement:loadingPermissions": "Loading permissions...",
        "roleManagement:cancel": "Cancel",
        "roleManagement:saving": "Saving",
        "roleManagement:updateRole": "Update Role",
        "roleManagement:createRole": "Create Role",
        "roleManagement:validation.name.required": "Role name is required",
        "roleManagement:validation.name.min":
          "Role name must be at least 3 characters",
        "roleManagement:validation.permissions.required":
          "At least one permission is required",

        // Without namespace (for direct key lookup)
        roleName: "Role Name",
        description: "Description",
        permissions: "Permissions",
        addPermission: "Add Permission",
        selectPermission: "Select a permission",
        noPermissionsAvailable: "No permissions available",
        noPermissions: "No permissions added",
        loadingPermissions: "Loading permissions...",
        cancel: "Cancel",
        saving: "Saving",
        updateRole: "Update Role",
        createRole: "Create Role",
        "validation.name.required": "Role name is required",
        "validation.name.min": "Role name must be at least 3 characters",
        "validation.permissions.required":
          "At least one permission is required",
      };

      return translations[key] || translations[keyWithoutNamespace] || key;
    },
  }),
}));

describe("RoleForm", () => {
  const mockOnSuccess = vi.fn();
  const mockCreateRole = vi.fn().mockResolvedValue({});
  const mockUpdateRole = vi.fn().mockResolvedValue({});

  const mockAvailablePermissions = [
    "permission:create",
    "permission:edit",
    "permission:delete",
  ];

  const mockRole: RoleData = {
    id: 1,
    name: "Test Role",
    description: "Test Description",
    permissions: ["permission:create", "permission:edit"],
    createdAt: "2023-01-01T00:00:00.000Z",
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mocks
    mockUseRoleMutation.mockReturnValue({
      createRole: mockCreateRole,
      updateRole: mockUpdateRole,
      isLoading: false,
    });

    mockUsePermissionsQuery.mockReturnValue({
      data: mockAvailablePermissions,
      isLoading: false,
    });
  });

  it("renders the form with default values in create mode", () => {
    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Check form fields are present - use more flexible text matching
    expect(screen.getByText("Role Name")).toBeInTheDocument();
    expect(screen.getByText("*")).toBeInTheDocument(); // Check asterisk separately
    expect(screen.getByText("Description")).toBeInTheDocument();

    // Check permissions section
    expect(screen.getByText("Permissions")).toBeInTheDocument();
    expect(screen.getByText("No permissions added")).toBeInTheDocument();

    // Check buttons
    expect(
      screen.getByRole("button", { name: /create role/i })
    ).toBeInTheDocument();
    expect(screen.getByRole("link", { name: /cancel/i })).toHaveAttribute(
      "href",
      "/system-settings/role"
    );
  });

  it("renders the form with role data in edit mode", () => {
    render(<RoleForm role={mockRole} onSuccess={mockOnSuccess} />);

    // Check form fields are present
    expect(screen.getByText(/Role Name/i)).toBeInTheDocument();
    expect(screen.getByText(/Description/i)).toBeInTheDocument();

    // Check permissions are displayed
    mockRole.permissions.forEach((permission) => {
      expect(screen.getByText(permission)).toBeInTheDocument();
    });

    // Check update button is shown instead of create
    expect(
      screen.getByRole("button", { name: /update role/i })
    ).toBeInTheDocument();
  });

  it("calls createRole when form is submitted in create mode", async () => {
    // Reset mocks
    mockCreateRole.mockClear();
    mockOnSuccess.mockClear();

    // Setup mocks
    mockCreateRole.mockResolvedValue({});

    // Make sure the useRoleMutation mock is properly set up
    mockUseRoleMutation.mockReturnValue({
      createRole: mockCreateRole,
      updateRole: mockUpdateRole,
      isLoading: false,
    });

    // Render the component
    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Manually trigger the form submission by getting the submit button and clicking it
    const submitButton = screen.getByText("Create Role");
    fireEvent.click(submitButton);

    // Wait for the async operations to complete
    await waitFor(
      () => {
        // Check that createRole was called
        expect(mockCreateRole).toHaveBeenCalled();

        // Check that onSuccess was called
        expect(mockOnSuccess).toHaveBeenCalled();
      },
      { timeout: 5000 }
    );
  });

  it("calls updateRole when form is submitted in edit mode", async () => {
    // Reset mocks
    mockUpdateRole.mockClear();
    mockOnSuccess.mockClear();

    // Setup mocks
    mockUpdateRole.mockResolvedValue({});

    // Make sure the useRoleMutation mock is properly set up
    mockUseRoleMutation.mockReturnValue({
      createRole: mockCreateRole,
      updateRole: mockUpdateRole,
      isLoading: false,
    });

    // Render the component
    render(<RoleForm role={mockRole} onSuccess={mockOnSuccess} />);

    // Manually trigger the form submission by getting the submit button and clicking it
    const submitButton = screen.getByText("Update Role");
    fireEvent.click(submitButton);

    // Wait for the async operations to complete
    await waitFor(
      () => {
        // Check that updateRole was called
        expect(mockUpdateRole).toHaveBeenCalled();

        // Check that onSuccess was called
        expect(mockOnSuccess).toHaveBeenCalled();
      },
      { timeout: 5000 }
    );
  });

  it("shows loading state when form is submitting", () => {
    mockUseRoleMutation.mockReturnValue({
      createRole: mockCreateRole,
      updateRole: mockUpdateRole,
      isLoading: true,
    });

    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Should show loading state on submit button
    expect(screen.getByRole("button", { name: /saving.../i })).toBeDisabled();
  });

  it("allows adding and removing permissions", async () => {
    // Mock permissions data - should be array of strings, not objects
    mockUsePermissionsQuery.mockReturnValue({
      data: ["permission:create", "permission:read"],
      isLoading: false,
    });

    // Create a mock for the form values
    let formValues = { permissions: [] };

    // Create a mock setValue function that updates formValues
    const mockSetValue = vi.fn((field, value) => {
      if (field === "permissions") {
        formValues.permissions = value;
      }
    });

    // Create a mock getValues function that returns formValues
    const mockGetValues = vi.fn((field) => {
      if (field === "permissions") {
        return formValues.permissions;
      }
      return formValues;
    });

    // Override the useForm mock for this test
    (useForm as jest.Mock).mockImplementation(() => ({
      formState: { errors: {} },
      watch: vi.fn((field) => (field ? formValues[field] : formValues)),
      getValues: mockGetValues,
      setValue: mockSetValue,
      handleSubmit: vi.fn(),
      reset: vi.fn(),
      control: {},
    }));

    // Reset the MockPermissionsTable component before the test
    MockPermissionsTable.mockClear();

    const { rerender } = render(<RoleForm onSuccess={mockOnSuccess} />);

    // Verify initial state
    expect(MockPermissionsTable).toHaveBeenCalledWith(
      expect.objectContaining({
        permissions: [],
        isLoading: false,
        loadingText: "Loading permissions...",
        noPermissionsText: "No permissions added",
        onRemovePermission: expect.any(Function),
      })
    );

    // Simulate adding a permission
    if (mockPermissionsComboboxOnSelect) {
      // Call the onSelect function with a permission
      mockPermissionsComboboxOnSelect("permission:create");

      // Verify setValue was called with the correct arguments
      expect(mockSetValue).toHaveBeenCalledWith(
        "permissions",
        ["permission:create"],
        expect.anything()
      );

      // Force rerender with updated permissions
      rerender(<RoleForm onSuccess={mockOnSuccess} />);

      // Verify the PermissionsTable was called with the updated permissions
      expect(MockPermissionsTable).toHaveBeenCalledWith(
        expect.objectContaining({
          permissions: ["permission:create"],
          isLoading: false,
          loadingText: "Loading permissions...",
          noPermissionsText: "No permissions added",
          onRemovePermission: expect.any(Function),
        })
      );

      // Simulate removing a permission
      if (mockPermissionsTableOnRemove) {
        // Call the onRemove function with the permission
        mockPermissionsTableOnRemove("permission:create");

        // Verify setValue was called with the correct arguments
        expect(mockSetValue).toHaveBeenCalledWith(
          "permissions",
          [],
          expect.anything()
        );

        // Force rerender with updated permissions
        rerender(<RoleForm onSuccess={mockOnSuccess} />);

        // Verify the PermissionsTable was called with the updated permissions
        expect(MockPermissionsTable).toHaveBeenCalledWith(
          expect.objectContaining({
            permissions: [],
            isLoading: false,
            loadingText: "Loading permissions...",
            noPermissionsText: "No permissions added",
            onRemovePermission: expect.any(Function),
          })
        );
      }
    }
  });

  it("shows loading state when permissions are loading", () => {
    // Set isLoading to true for permissions query
    mockUsePermissionsQuery.mockReturnValue({
      data: [],
      isLoading: true,
    });

    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Check if the permissions table shows loading state
    const permissionsTable = screen.getByTestId("permissions-table");
    expect(permissionsTable).toBeInTheDocument();
    expect(permissionsTable.textContent).toContain("Loading permissions...");
  });

  it("handles form submission errors gracefully", async () => {
    // Mock createRole to throw an error
    const mockError = new Error("Network error");
    mockCreateRole.mockRejectedValue(mockError);

    // Spy on console.error to verify error logging
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    // Create a working handleSubmit mock that actually calls the onSubmit function
    const mockHandleSubmit = vi.fn().mockImplementation((onSubmit) => (e) => {
      e?.preventDefault();
      // Call onSubmit with default form values
      onSubmit({
        name: "",
        description: "",
        permissions: [],
      });
    });

    // Override the useForm mock to use the working handleSubmit
    (useForm as jest.Mock).mockImplementation(() => ({
      formState: { errors: {} },
      watch: vi.fn().mockReturnValue([]),
      getValues: vi.fn().mockReturnValue({ permissions: [] }),
      setValue: vi.fn(),
      handleSubmit: mockHandleSubmit,
      reset: vi.fn(),
      control: {},
    }));

    mockUseRoleMutation.mockReturnValue({
      createRole: mockCreateRole,
      updateRole: mockUpdateRole,
      isLoading: false,
    });

    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Submit the form
    const submitButton = screen.getByText("Create Role");
    fireEvent.click(submitButton);

    // Wait for the error to be handled
    await waitFor(() => {
      expect(mockCreateRole).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith("Error saving role:", mockError);
      // onSuccess should not be called when there's an error
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    consoleSpy.mockRestore();
  });

  it("prevents adding duplicate permissions", async () => {
    // Create a mock for the form values with existing permissions
    let formValues = { permissions: ["permission:create"] };

    const mockSetValue = vi.fn((field, value) => {
      if (field === "permissions") {
        formValues.permissions = value;
      }
    });

    const mockGetValues = vi.fn((field) => {
      if (field === "permissions") {
        return formValues.permissions;
      }
      return formValues;
    });

    // Override the useForm mock for this test
    (useForm as jest.Mock).mockImplementation(() => ({
      formState: { errors: {} },
      watch: vi.fn((field) => (field ? formValues[field] : formValues)),
      getValues: mockGetValues,
      setValue: mockSetValue,
      handleSubmit: vi.fn(),
      reset: vi.fn(),
      control: {},
    }));

    render(<RoleForm onSuccess={mockOnSuccess} />);

    // Try to add the same permission that already exists
    if (mockPermissionsComboboxOnSelect) {
      mockPermissionsComboboxOnSelect("permission:create");

      // setValue should not be called since the permission already exists
      expect(mockSetValue).not.toHaveBeenCalled();
    }
  });
});
