
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for bonito-fe-csr/src/components/article-list/utils.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">bonito-fe-csr/src/components/article-list</a> utils.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/126</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/126</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React from "react";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { FileText, Video, ImageIcon } from "lucide-react";</span>
import type { ArticleItem } from "./types";
<span class="cstat-no" title="statement not covered" >import { availableClusters } from "./constants";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >// 獲取來源圖示</span>
<span class="cstat-no" title="statement not covered" >export const getSourceIcon = (sourceName: string): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const iconMap: { [key: string]: string } = {</span>
<span class="cstat-no" title="statement not covered" >    聯合新聞網: "📰",</span>
<span class="cstat-no" title="statement not covered" >    自由時報: "📰",</span>
<span class="cstat-no" title="statement not covered" >    中時新聞網: "📰",</span>
<span class="cstat-no" title="statement not covered" >    ETtoday: "📰",</span>
<span class="cstat-no" title="statement not covered" >    TVBS新聞: "📺",</span>
<span class="cstat-no" title="statement not covered" >    Facebook: "📘",</span>
<span class="cstat-no" title="statement not covered" >    Instagram: "📷",</span>
<span class="cstat-no" title="statement not covered" >    Twitter: "🐦",</span>
<span class="cstat-no" title="statement not covered" >    TikTok: "🎵",</span>
<span class="cstat-no" title="statement not covered" >    YouTube: "🎥",</span>
<span class="cstat-no" title="statement not covered" >    PTT: "💬",</span>
<span class="cstat-no" title="statement not covered" >    Dcard: "🎓",</span>
<span class="cstat-no" title="statement not covered" >    Mobile01: "📱",</span>
<span class="cstat-no" title="statement not covered" >    巴哈姆特: "🎮",</span>
<span class="cstat-no" title="statement not covered" >    Google評論: "⭐",</span>
<span class="cstat-no" title="statement not covered" >    "App Store": "📱",</span>
<span class="cstat-no" title="statement not covered" >    "Google Play": "🤖",</span>
<span class="cstat-no" title="statement not covered" >    Tripadvisor: "✈️",</span>
<span class="cstat-no" title="statement not covered" >    痞客邦: "📝",</span>
<span class="cstat-no" title="statement not covered" >    Medium: "📖",</span>
<span class="cstat-no" title="statement not covered" >    個人部落格: "📝",</span>
<span class="cstat-no" title="statement not covered" >    專業媒體: "📰",</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >  return iconMap[sourceName] || "📄";</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const getSentimentColor = (sentiment: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  switch (sentiment) {</span>
<span class="cstat-no" title="statement not covered" >    case "positive":</span>
<span class="cstat-no" title="statement not covered" >      return "text-green-600 bg-green-50";</span>
<span class="cstat-no" title="statement not covered" >    case "negative":</span>
<span class="cstat-no" title="statement not covered" >      return "text-red-600 bg-red-50";</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      return "text-gray-600 bg-gray-50";</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const getMediaTypeIcon = (mediaType: string): React.ReactElement =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  switch (mediaType) {</span>
<span class="cstat-no" title="statement not covered" >    case "video":</span>
<span class="cstat-no" title="statement not covered" >      return &lt;Video className="w-4 h-4" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    case "image":</span>
<span class="cstat-no" title="statement not covered" >      return &lt;ImageIcon className="w-4 h-4" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      return &lt;FileText className="w-4 h-4" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >// 生成測試文章資料，包含群集、來源、板塊資訊</span>
<span class="cstat-no" title="statement not covered" >export const generateSampleArticles = (): ArticleItem[] =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const baseArticles: Partial&lt;ArticleItem&gt;[] = [</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      id: "1",</span>
<span class="cstat-no" title="statement not covered" >      title:</span>
<span class="cstat-no" title="statement not covered" >        "【完整版中字】英雄：道345會出現社中體帶版　增10：第20出些錯來界廊？20250617｜#林系翼君虎經社中資誠編論學文系體景第元之修樓活圖",</span>
<span class="cstat-no" title="statement not covered" >      content: "道然期不暖幸了！完沒會實想迴實！😍😍😍 暖淋也不環了！",</span>
<span class="cstat-no" title="statement not covered" >      author: "@kevin03",</span>
<span class="cstat-no" title="statement not covered" >      publishedAt: new Date("2025-06-19 11:04:14"),</span>
<span class="cstat-no" title="statement not covered" >      metrics: { views: 11600, likes: 503, shares: 26, comments: 89 },</span>
<span class="cstat-no" title="statement not covered" >      sentiment: "positive",</span>
<span class="cstat-no" title="statement not covered" >      category: "娛樂",</span>
<span class="cstat-no" title="statement not covered" >      tags: ["政治", "新聞"],</span>
<span class="cstat-no" title="statement not covered" >      url: "https://youtube.com/watch?v=123",</span>
<span class="cstat-no" title="statement not covered" >      mediaType: "video",</span>
<span class="cstat-no" title="statement not covered" >      verified: false,</span>
<span class="cstat-no" title="statement not covered" >      clusterId: "2",</span>
<span class="cstat-no" title="statement not covered" >      sourceId: "youtube",</span>
<span class="cstat-no" title="statement not covered" >      platform: "YouTube",</span>
<span class="cstat-no" title="statement not covered" >      platformIcon: "🎥",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      id: "2",</span>
<span class="cstat-no" title="statement not covered" >      title:</span>
<span class="cstat-no" title="statement not covered" >        "今天我和違處安市長、陳錫華議員、台北市議會李伯蘇，議市議會員地方…",</span>
<span class="cstat-no" title="statement not covered" >      content: "全球不信贏樂約告！",</span>
<span class="cstat-no" title="statement not covered" >      author: "陳其邁",</span>
<span class="cstat-no" title="statement not covered" >      publishedAt: new Date("2025-06-19 09:07:32"),</span>
<span class="cstat-no" title="statement not covered" >      metrics: { views: 12700, likes: 445, shares: 34, comments: 127 },</span>
<span class="cstat-no" title="statement not covered" >      sentiment: "neutral",</span>
<span class="cstat-no" title="statement not covered" >      category: "政治",</span>
<span class="cstat-no" title="statement not covered" >      tags: ["市政", "政治人物"],</span>
<span class="cstat-no" title="statement not covered" >      url: "https://facebook.com/post/456",</span>
<span class="cstat-no" title="statement not covered" >      mediaType: "text",</span>
<span class="cstat-no" title="statement not covered" >      verified: true,</span>
<span class="cstat-no" title="statement not covered" >      clusterId: "2",</span>
<span class="cstat-no" title="statement not covered" >      sourceId: "facebook",</span>
<span class="cstat-no" title="statement not covered" >      platform: "Facebook",</span>
<span class="cstat-no" title="statement not covered" >      platformIcon: "📘",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >  ];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // 生成更多測試文章</span>
<span class="cstat-no" title="statement not covered" >  const additionalArticles = Array.from({ length: 30 }, (_, i) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const cluster = availableClusters[i % availableClusters.length];</span>
<span class="cstat-no" title="statement not covered" >    const source = cluster.sources[i % cluster.sources.length];</span>
<span class="cstat-no" title="statement not covered" >    const board = source.boards?.[i % (source.boards?.length || 1)];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      id: `${i + 3}`,</span>
<span class="cstat-no" title="statement not covered" >      title: `測試文章標題 ${i + 3} - 這是一個用來測試滾動功能的長標題，包含各種關鍵字和內容，確保有足夠的內容可以滾動查看`,</span>
<span class="cstat-no" title="statement not covered" >      content: `這是第 ${i + 3} 篇測試文章的內容。內容包含了各種社群媒體的討論話題，用來測試系統的搜尋和篩選功能。這裡有更多的文字內容來確保每個文章卡片都有足夠的高度，讓滾動功能更明顯。`,</span>
<span class="cstat-no" title="statement not covered" >      author: `測試用戶${i + 3}`,</span>
<span class="cstat-no" title="statement not covered" >      publishedAt: new Date(2025, 5, 19 - i, 10, 0, 0),</span>
<span class="cstat-no" title="statement not covered" >      metrics: {</span>
<span class="cstat-no" title="statement not covered" >        views: Math.floor(Math.random() * 50000) + 1000,</span>
<span class="cstat-no" title="statement not covered" >        likes: Math.floor(Math.random() * 1000) + 50,</span>
<span class="cstat-no" title="statement not covered" >        shares: Math.floor(Math.random() * 100) + 5,</span>
<span class="cstat-no" title="statement not covered" >        comments: Math.floor(Math.random() * 200) + 10,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      sentiment: ["positive", "neutral", "negative"][i % 3] as</span>
        | "positive"
        | "neutral"
        | "negative",
<span class="cstat-no" title="statement not covered" >      category: ["政治", "娛樂", "科技", "體育"][i % 4],</span>
<span class="cstat-no" title="statement not covered" >      tags: [`標籤${i + 1}`, `分類${(i % 3) + 1}`],</span>
<span class="cstat-no" title="statement not covered" >      url: `https://example.com/post/${i + 3}`,</span>
<span class="cstat-no" title="statement not covered" >      mediaType: ["text", "image", "video"][i % 3] as</span>
        | "text"
        | "image"
        | "video",
<span class="cstat-no" title="statement not covered" >      verified: i % 4 === 0,</span>
<span class="cstat-no" title="statement not covered" >      clusterId: cluster.id,</span>
<span class="cstat-no" title="statement not covered" >      sourceId: source.id,</span>
<span class="cstat-no" title="statement not covered" >      platform: source.name,</span>
<span class="cstat-no" title="statement not covered" >      platformIcon: getSourceIcon(source.name),</span>
<span class="cstat-no" title="statement not covered" >      board: board?.name,</span>
<span class="cstat-no" title="statement not covered" >      boardId: board?.id,</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >  });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return [...baseArticles, ...additionalArticles] as ArticleItem[];</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:00:40.781Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    