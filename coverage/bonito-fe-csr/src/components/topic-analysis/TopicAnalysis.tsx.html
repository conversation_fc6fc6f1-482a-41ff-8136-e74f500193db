
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for bonito-fe-csr/src/components/topic-analysis/TopicAnalysis.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">bonito-fe-csr/src/components/topic-analysis</a> TopicAnalysis.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/230</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/230</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { TopicAnalysisResults } from "@/components/topic-analysis-results/topic-analysis-results";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { useCallback, useEffect, useState } from "react";</span>
<span class="cstat-no" title="statement not covered" >import { useTranslation } from "react-i18next";</span>
<span class="cstat-no" title="statement not covered" >import { AnalysisRecordsSidebar } from "./components/AnalysisRecordsSidebar";</span>
<span class="cstat-no" title="statement not covered" >import { SearchForm } from "./components/SearchForm";</span>
<span class="cstat-no" title="statement not covered" >import { initialAnalysisRecords, summaryTemplates } from "./constants";</span>
<span class="cstat-no" title="statement not covered" >import { useAnalysisRecords } from "./hooks/useAnalysisRecords";</span>
<span class="cstat-no" title="statement not covered" >import { useSearchForm } from "./hooks/useSearchForm";</span>
<span class="cstat-no" title="statement not covered" >import { useUrlParams } from "./hooks/useUrlParams";</span>
import type { AnalysisRecord } from "./types";
&nbsp;
<span class="cstat-no" title="statement not covered" >export function TopicAnalysis() {</span>
<span class="cstat-no" title="statement not covered" >  const { t } = useTranslation("topicAnalysis");</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // Custom hooks for state management</span>
<span class="cstat-no" title="statement not covered" >  const { queryParam, showResultsParam } = useUrlParams();</span>
<span class="cstat-no" title="statement not covered" >  const {</span>
<span class="cstat-no" title="statement not covered" >    analysisRecords,</span>
<span class="cstat-no" title="statement not covered" >    setAnalysisRecords,</span>
<span class="cstat-no" title="statement not covered" >    currentAnalysis,</span>
<span class="cstat-no" title="statement not covered" >    setCurrentAnalysis,</span>
<span class="cstat-no" title="statement not covered" >    handleRecordClick,</span>
<span class="cstat-no" title="statement not covered" >    handleClearRecord,</span>
<span class="cstat-no" title="statement not covered" >  } = useAnalysisRecords();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const {</span>
<span class="cstat-no" title="statement not covered" >    searchQuery,</span>
<span class="cstat-no" title="statement not covered" >    setSearchQuery,</span>
<span class="cstat-no" title="statement not covered" >    startDate,</span>
<span class="cstat-no" title="statement not covered" >    setStartDate,</span>
<span class="cstat-no" title="statement not covered" >    endDate,</span>
<span class="cstat-no" title="statement not covered" >    setEndDate,</span>
<span class="cstat-no" title="statement not covered" >    selectedClusters,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedClusters,</span>
<span class="cstat-no" title="statement not covered" >    selectedSources,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedSources,</span>
<span class="cstat-no" title="statement not covered" >    selectedBoards,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedBoards,</span>
<span class="cstat-no" title="statement not covered" >    selectedKeywordGroups,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedKeywordGroups,</span>
<span class="cstat-no" title="statement not covered" >    showClusterSelector,</span>
<span class="cstat-no" title="statement not covered" >    setShowClusterSelector,</span>
<span class="cstat-no" title="statement not covered" >    showKeywordSelector,</span>
<span class="cstat-no" title="statement not covered" >    setShowKeywordSelector,</span>
<span class="cstat-no" title="statement not covered" >    isLoading,</span>
<span class="cstat-no" title="statement not covered" >    setIsLoading,</span>
<span class="cstat-no" title="statement not covered" >  } = useSearchForm(queryParam || "", "2024-06-01", "2025-05-31");</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // Local state</span>
<span class="cstat-no" title="statement not covered" >  const [showResults, setShowResults] = useState(showResultsParam === "true");</span>
<span class="cstat-no" title="statement not covered" >  const [searchTerm, setSearchTerm] = useState("");</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // Helper functions</span>
<span class="cstat-no" title="statement not covered" >  const generateSummary = useCallback((): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return summaryTemplates[</span>
<span class="cstat-no" title="statement not covered" >      Math.floor(Math.random() * summaryTemplates.length)</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
<span class="cstat-no" title="statement not covered" >  }, []);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSearch = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!searchQuery.trim()) return;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    setIsLoading(true);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    // Add to analysis records</span>
<span class="cstat-no" title="statement not covered" >    const newAnalysisRecord: AnalysisRecord = {</span>
<span class="cstat-no" title="statement not covered" >      id: Date.now().toString(),</span>
<span class="cstat-no" title="statement not covered" >      query: searchQuery,</span>
<span class="cstat-no" title="statement not covered" >      timestamp: new Date(),</span>
<span class="cstat-no" title="statement not covered" >      resultCount: Math.floor(Math.random() * 3000) + 1000,</span>
<span class="cstat-no" title="statement not covered" >      status: "analyzing",</span>
<span class="cstat-no" title="statement not covered" >      summary: t("messages.analyzingInProgress"),</span>
<span class="cstat-no" title="statement not covered" >      clusters: selectedClusters,</span>
<span class="cstat-no" title="statement not covered" >      keywordGroups: selectedKeywordGroups,</span>
<span class="cstat-no" title="statement not covered" >      dateRange: {</span>
<span class="cstat-no" title="statement not covered" >        start: startDate,</span>
<span class="cstat-no" title="statement not covered" >        end: endDate,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      selectedSources: selectedSources,</span>
<span class="cstat-no" title="statement not covered" >      selectedBoards: selectedBoards,</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    setAnalysisRecords((prev) =&gt; [newAnalysisRecord, ...prev]);</span>
<span class="cstat-no" title="statement not covered" >    setCurrentAnalysis(newAnalysisRecord);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    // Simulate search delay</span>
<span class="cstat-no" title="statement not covered" >    setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const completedRecord = {</span>
<span class="cstat-no" title="statement not covered" >        ...newAnalysisRecord,</span>
<span class="cstat-no" title="statement not covered" >        status: "completed" as const,</span>
<span class="cstat-no" title="statement not covered" >        summary: generateSummary(),</span>
<span class="cstat-no" title="statement not covered" >      };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      setAnalysisRecords((prev) =&gt;</span>
<span class="cstat-no" title="statement not covered" >        prev.map((record) =&gt;</span>
<span class="cstat-no" title="statement not covered" >          record.id === newAnalysisRecord.id ? completedRecord : record,</span>
        ),
      );
<span class="cstat-no" title="statement not covered" >      setCurrentAnalysis(completedRecord);</span>
<span class="cstat-no" title="statement not covered" >      setIsLoading(false);</span>
<span class="cstat-no" title="statement not covered" >      setShowResults(true);</span>
<span class="cstat-no" title="statement not covered" >    }, 2000);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleRecordClickWithState = (record: AnalysisRecord) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    handleRecordClick(record);</span>
<span class="cstat-no" title="statement not covered" >    if (record.status === "completed") {</span>
<span class="cstat-no" title="statement not covered" >      setSearchQuery(record.query);</span>
<span class="cstat-no" title="statement not covered" >      setSelectedClusters(record.clusters);</span>
<span class="cstat-no" title="statement not covered" >      setSelectedSources(record.selectedSources || {});</span>
<span class="cstat-no" title="statement not covered" >      setSelectedBoards(record.selectedBoards || {});</span>
<span class="cstat-no" title="statement not covered" >      setShowResults(true);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSuggestedTopicClick = (topic: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSearchQuery(topic);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSourceChange = (clusterId: string, sourceIds: string[]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedSources((prev) =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >      ...prev,</span>
<span class="cstat-no" title="statement not covered" >      [clusterId]: sourceIds,</span>
<span class="cstat-no" title="statement not covered" >    }));</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleBoardChange = (sourceId: string, boardIds: string[]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedBoards((prev) =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >      ...prev,</span>
<span class="cstat-no" title="statement not covered" >      [sourceId]: boardIds,</span>
<span class="cstat-no" title="statement not covered" >    }));</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const startNewAnalysis = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setCurrentAnalysis(null);</span>
<span class="cstat-no" title="statement not covered" >    setSearchQuery("");</span>
<span class="cstat-no" title="statement not covered" >    setShowResults(false);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // URL parameter handling effect</span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (queryParam &amp;&amp; showResultsParam === "true") {</span>
<span class="cstat-no" title="statement not covered" >      // Find corresponding analysis record</span>
<span class="cstat-no" title="statement not covered" >      const existingRecord = initialAnalysisRecords.find(</span>
<span class="cstat-no" title="statement not covered" >        (record) =&gt; record.query === queryParam,</span>
      );
<span class="cstat-no" title="statement not covered" >      if (existingRecord) {</span>
<span class="cstat-no" title="statement not covered" >        setCurrentAnalysis(existingRecord);</span>
<span class="cstat-no" title="statement not covered" >        setSelectedClusters(existingRecord.clusters);</span>
<span class="cstat-no" title="statement not covered" >        setSelectedSources(existingRecord.selectedSources || {});</span>
<span class="cstat-no" title="statement not covered" >        setSelectedBoards(existingRecord.selectedBoards || {});</span>
<span class="cstat-no" title="statement not covered" >        setShowResults(true);</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        // 如果沒有找到記錄，創建一個新的</span>
<span class="cstat-no" title="statement not covered" >        const newRecord: AnalysisRecord = {</span>
<span class="cstat-no" title="statement not covered" >          id: Date.now().toString(),</span>
<span class="cstat-no" title="statement not covered" >          query: queryParam,</span>
<span class="cstat-no" title="statement not covered" >          timestamp: new Date(),</span>
<span class="cstat-no" title="statement not covered" >          resultCount: Math.floor(Math.random() * 30000) + 1000,</span>
<span class="cstat-no" title="statement not covered" >          status: "completed",</span>
<span class="cstat-no" title="statement not covered" >          summary: generateSummary(),</span>
<span class="cstat-no" title="statement not covered" >          clusters: ["1", "2"],</span>
<span class="cstat-no" title="statement not covered" >          keywordGroups: [1, 2],</span>
<span class="cstat-no" title="statement not covered" >          dateRange: {</span>
<span class="cstat-no" title="statement not covered" >            start: startDate,</span>
<span class="cstat-no" title="statement not covered" >            end: endDate,</span>
<span class="cstat-no" title="statement not covered" >          },</span>
<span class="cstat-no" title="statement not covered" >        };</span>
<span class="cstat-no" title="statement not covered" >        setCurrentAnalysis(newRecord);</span>
<span class="cstat-no" title="statement not covered" >        setAnalysisRecords((prev) =&gt; [newRecord, ...prev]);</span>
<span class="cstat-no" title="statement not covered" >        setShowResults(true);</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [</span>
<span class="cstat-no" title="statement not covered" >    queryParam,</span>
<span class="cstat-no" title="statement not covered" >    showResultsParam,</span>
<span class="cstat-no" title="statement not covered" >    startDate,</span>
<span class="cstat-no" title="statement not covered" >    endDate,</span>
<span class="cstat-no" title="statement not covered" >    setCurrentAnalysis,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedClusters,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedSources,</span>
<span class="cstat-no" title="statement not covered" >    setSelectedBoards,</span>
<span class="cstat-no" title="statement not covered" >    setShowResults,</span>
<span class="cstat-no" title="statement not covered" >    setAnalysisRecords,</span>
<span class="cstat-no" title="statement not covered" >    generateSummary,</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="flex h-[calc(100vh-73px)]"&gt;</span>
      {/* Left Sidebar - Analysis Records */}
<span class="cstat-no" title="statement not covered" >      &lt;AnalysisRecordsSidebar</span>
<span class="cstat-no" title="statement not covered" >        analysisRecords={analysisRecords}</span>
<span class="cstat-no" title="statement not covered" >        currentAnalysis={currentAnalysis}</span>
<span class="cstat-no" title="statement not covered" >        searchTerm={searchTerm}</span>
<span class="cstat-no" title="statement not covered" >        onSearchTermChange={setSearchTerm}</span>
<span class="cstat-no" title="statement not covered" >        onRecordClick={handleRecordClickWithState}</span>
<span class="cstat-no" title="statement not covered" >        onClearRecord={handleClearRecord}</span>
<span class="cstat-no" title="statement not covered" >        onStartNewAnalysis={startNewAnalysis}</span>
<span class="cstat-no" title="statement not covered" >        onSuggestedTopicClick={handleSuggestedTopicClick}</span>
      /&gt;
&nbsp;
      {/* Main Content Area */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="flex-1 flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {!showResults ? (</span>
<span class="cstat-no" title="statement not covered" >          /* Search Form */</span>
<span class="cstat-no" title="statement not covered" >          &lt;SearchForm</span>
<span class="cstat-no" title="statement not covered" >            searchQuery={searchQuery}</span>
<span class="cstat-no" title="statement not covered" >            onSearchQueryChange={setSearchQuery}</span>
<span class="cstat-no" title="statement not covered" >            startDate={startDate}</span>
<span class="cstat-no" title="statement not covered" >            endDate={endDate}</span>
<span class="cstat-no" title="statement not covered" >            onStartDateChange={setStartDate}</span>
<span class="cstat-no" title="statement not covered" >            onEndDateChange={setEndDate}</span>
<span class="cstat-no" title="statement not covered" >            selectedClusters={selectedClusters}</span>
<span class="cstat-no" title="statement not covered" >            onClusterChange={setSelectedClusters}</span>
<span class="cstat-no" title="statement not covered" >            selectedSources={selectedSources}</span>
<span class="cstat-no" title="statement not covered" >            onSourceChange={handleSourceChange}</span>
<span class="cstat-no" title="statement not covered" >            selectedBoards={selectedBoards}</span>
<span class="cstat-no" title="statement not covered" >            onBoardChange={handleBoardChange}</span>
<span class="cstat-no" title="statement not covered" >            selectedKeywordGroups={selectedKeywordGroups}</span>
<span class="cstat-no" title="statement not covered" >            onKeywordGroupChange={setSelectedKeywordGroups}</span>
<span class="cstat-no" title="statement not covered" >            showClusterSelector={showClusterSelector}</span>
<span class="cstat-no" title="statement not covered" >            onToggleClusterSelector={() =&gt;</span>
<span class="cstat-no" title="statement not covered" >              setShowClusterSelector(!showClusterSelector)</span>
            }
<span class="cstat-no" title="statement not covered" >            showKeywordSelector={showKeywordSelector}</span>
<span class="cstat-no" title="statement not covered" >            onToggleKeywordSelector={() =&gt;</span>
<span class="cstat-no" title="statement not covered" >              setShowKeywordSelector(!showKeywordSelector)</span>
            }
<span class="cstat-no" title="statement not covered" >            isLoading={isLoading}</span>
<span class="cstat-no" title="statement not covered" >            onSearch={handleSearch}</span>
<span class="cstat-no" title="statement not covered" >            onSuggestedTopicClick={handleSuggestedTopicClick}</span>
          /&gt;
<span class="cstat-no" title="statement not covered" >        ) : !currentAnalysis ? (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center justify-center h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p&gt;No analysis data available&lt;/p&gt;</span>
          &lt;/div&gt;
        ) : (
<span class="cstat-no" title="statement not covered" >          &lt;TopicAnalysisResults</span>
<span class="cstat-no" title="statement not covered" >            query={currentAnalysis.query}</span>
<span class="cstat-no" title="statement not covered" >            result={{</span>
<span class="cstat-no" title="statement not covered" >              volumeTrend: [],</span>
<span class="cstat-no" title="statement not covered" >              sourceDistribution: [],</span>
<span class="cstat-no" title="statement not covered" >              sentimentData: {</span>
<span class="cstat-no" title="statement not covered" >                totalPositive: 0,</span>
<span class="cstat-no" title="statement not covered" >                totalNeutral: 0,</span>
<span class="cstat-no" title="statement not covered" >                totalNegative: 0,</span>
<span class="cstat-no" title="statement not covered" >                overallRatio: 0,</span>
<span class="cstat-no" title="statement not covered" >              },</span>
<span class="cstat-no" title="statement not covered" >              wordCloud: [],</span>
<span class="cstat-no" title="statement not covered" >              topPosts: [],</span>
<span class="cstat-no" title="statement not covered" >              platformAnalysis: [],</span>
<span class="cstat-no" title="statement not covered" >              hotTopics: [],</span>
<span class="cstat-no" title="statement not covered" >              hotArticles: [],</span>
<span class="cstat-no" title="statement not covered" >              mediaSourceCategories: [],</span>
<span class="cstat-no" title="statement not covered" >              topMediaSources: [],</span>
<span class="cstat-no" title="statement not covered" >              platformActivity: [],</span>
<span class="cstat-no" title="statement not covered" >              resultCount: currentAnalysis.resultCount,</span>
<span class="cstat-no" title="statement not covered" >              timestamp: currentAnalysis.timestamp.toISOString(),</span>
<span class="cstat-no" title="statement not covered" >            }}</span>
<span class="cstat-no" title="statement not covered" >            onBack={startNewAnalysis}</span>
          /&gt;
        )}
      &lt;/div&gt;
    &lt;/div&gt;
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:00:40.781Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    