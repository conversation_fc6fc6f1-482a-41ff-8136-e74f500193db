
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for bonito-fe-csr/src/components/main-dashboard.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">bonito-fe-csr/src/components</a> main-dashboard.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/147</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/147</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import type React from "react";
&nbsp;
<span class="cstat-no" title="statement not covered" >import {</span>
  DataSourcesSection,
  HeroSection,
  RecentAnalysisSection,
  type AnalysisRecord,
} from "@/components/dashboard";
<span class="cstat-no" title="statement not covered" >import { useEffect, useState } from "react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default function MainDashboard() {</span>
<span class="cstat-no" title="statement not covered" >  const [analysisRecords] = useState&lt;AnalysisRecord[]&gt;([</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      id: "1",</span>
<span class="cstat-no" title="statement not covered" >      query: "黃建賓",</span>
<span class="cstat-no" title="statement not covered" >      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >      resultCount: 30927,</span>
<span class="cstat-no" title="statement not covered" >      status: "completed",</span>
<span class="cstat-no" title="statement not covered" >      summary: "政治相關討論為主，情緒好感度為0.34",</span>
<span class="cstat-no" title="statement not covered" >      dateRange: "2024/06/01 至 2025/05/31",</span>
<span class="cstat-no" title="statement not covered" >      sentiment: {</span>
<span class="cstat-no" title="statement not covered" >        positive: 11,</span>
<span class="cstat-no" title="statement not covered" >        neutral: 56,</span>
<span class="cstat-no" title="statement not covered" >        negative: 33,</span>
<span class="cstat-no" title="statement not covered" >        goodwillScore: 0.34,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      overview: {</span>
<span class="cstat-no" title="statement not covered" >        totalVolume: 30927,</span>
<span class="cstat-no" title="statement not covered" >        monthlyChange: 23.4,</span>
<span class="cstat-no" title="statement not covered" >        mainSource: "社群網站",</span>
<span class="cstat-no" title="statement not covered" >        sourcePercentage: 69.13,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      wordCloud: [</span>
<span class="cstat-no" title="statement not covered" >        { text: "國民黨", size: 60 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "藍委", size: 58 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "中選會", size: 55 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "立法院", size: 52 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "提案", size: 48 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "黃建賓", size: 45 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "賴清德", size: 42 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "還區", size: 40 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "死刑", size: 38 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "送件", size: 35 },</span>
<span class="cstat-no" title="statement not covered" >      ],</span>
<span class="cstat-no" title="statement not covered" >      lastUpdate: "2 分鐘前",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      id: "2",</span>
<span class="cstat-no" title="statement not covered" >      query: "Samsung Galaxy S25",</span>
<span class="cstat-no" title="statement not covered" >      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >      resultCount: 3187,</span>
<span class="cstat-no" title="statement not covered" >      status: "completed",</span>
<span class="cstat-no" title="statement not covered" >      summary: "相機功能受到好評，充電速度有待改善",</span>
<span class="cstat-no" title="statement not covered" >      dateRange: "2025/01/10 至 2025/01/20",</span>
<span class="cstat-no" title="statement not covered" >      sentiment: {</span>
<span class="cstat-no" title="statement not covered" >        positive: 45,</span>
<span class="cstat-no" title="statement not covered" >        neutral: 35,</span>
<span class="cstat-no" title="statement not covered" >        negative: 20,</span>
<span class="cstat-no" title="statement not covered" >        goodwillScore: 0.62,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      overview: {</span>
<span class="cstat-no" title="statement not covered" >        totalVolume: 3187,</span>
<span class="cstat-no" title="statement not covered" >        monthlyChange: -8.7,</span>
<span class="cstat-no" title="statement not covered" >        mainSource: "社群媒體平台",</span>
<span class="cstat-no" title="statement not covered" >        sourcePercentage: 42.3,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      wordCloud: [</span>
<span class="cstat-no" title="statement not covered" >        { text: "相機", size: 55 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "充電", size: 50 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "性能", size: 45 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "價格", size: 40 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "設計", size: 35 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "螢幕", size: 30 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "電池", size: 28 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "功能", size: 25 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "品質", size: 22 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "推薦", size: 20 },</span>
<span class="cstat-no" title="statement not covered" >      ],</span>
<span class="cstat-no" title="statement not covered" >      lastUpdate: "5 分鐘前",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    {</span>
<span class="cstat-no" title="statement not covered" >      id: "3",</span>
<span class="cstat-no" title="statement not covered" >      query: "Google Pixel 9",</span>
<span class="cstat-no" title="statement not covered" >      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >      resultCount: 1845,</span>
<span class="cstat-no" title="statement not covered" >      status: "completed",</span>
<span class="cstat-no" title="statement not covered" >      summary: "AI功能創新獲得認可，但市場可見度不足",</span>
<span class="cstat-no" title="statement not covered" >      dateRange: "2025/01/05 至 2025/01/18",</span>
<span class="cstat-no" title="statement not covered" >      sentiment: {</span>
<span class="cstat-no" title="statement not covered" >        positive: 72,</span>
<span class="cstat-no" title="statement not covered" >        neutral: 18,</span>
<span class="cstat-no" title="statement not covered" >        negative: 10,</span>
<span class="cstat-no" title="statement not covered" >        goodwillScore: 0.81,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      overview: {</span>
<span class="cstat-no" title="statement not covered" >        totalVolume: 1845,</span>
<span class="cstat-no" title="statement not covered" >        monthlyChange: 23.8,</span>
<span class="cstat-no" title="statement not covered" >        mainSource: "評價平台",</span>
<span class="cstat-no" title="statement not covered" >        sourcePercentage: 38.7,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      wordCloud: [</span>
<span class="cstat-no" title="statement not covered" >        { text: "AI", size: 58 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "創新", size: 52 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "功能", size: 48 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "相機", size: 45 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "智能", size: 42 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "體驗", size: 38 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "技術", size: 35 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "品質", size: 32 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "設計", size: 28 },</span>
<span class="cstat-no" title="statement not covered" >        { text: "推薦", size: 25 },</span>
<span class="cstat-no" title="statement not covered" >      ],</span>
<span class="cstat-no" title="statement not covered" >      lastUpdate: "1 分鐘前",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >  ]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // 初始化為所有主題的ID</span>
<span class="cstat-no" title="statement not covered" >  const [selectedTopics, setSelectedTopics] = useState&lt;string[]&gt;([]);</span>
<span class="cstat-no" title="statement not covered" >  const [dropdownOpen, setDropdownOpen] = useState(false);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  // 在組件加載時，設置默認選中所有主題</span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedTopics(analysisRecords.map((record) =&gt; record.id));</span>
<span class="cstat-no" title="statement not covered" >  }, [analysisRecords]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleTopicToggle = (id: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setSelectedTopics((prev) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (prev.includes(id)) {</span>
<span class="cstat-no" title="statement not covered" >        return prev.filter((topicId) =&gt; topicId !== id);</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        return [...prev, id];</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    });</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSelectAll = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (selectedTopics.length === analysisRecords.length) {</span>
<span class="cstat-no" title="statement not covered" >      setSelectedTopics([]);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      setSelectedTopics(analysisRecords.map((record) =&gt; record.id));</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleViewAnalysis = (e: React.MouseEvent, query: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >    window.location.href = `/topic-analysis?query=${encodeURIComponent(query)}&amp;showResults=true`;</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;HeroSection /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;RecentAnalysisSection</span>
<span class="cstat-no" title="statement not covered" >        analysisRecords={analysisRecords}</span>
<span class="cstat-no" title="statement not covered" >        selectedTopics={selectedTopics}</span>
<span class="cstat-no" title="statement not covered" >        dropdownOpen={dropdownOpen}</span>
<span class="cstat-no" title="statement not covered" >        onDropdownToggle={() =&gt; setDropdownOpen(!dropdownOpen)}</span>
<span class="cstat-no" title="statement not covered" >        onTopicToggle={handleTopicToggle}</span>
<span class="cstat-no" title="statement not covered" >        onSelectAll={handleSelectAll}</span>
<span class="cstat-no" title="statement not covered" >        onViewAnalysis={handleViewAnalysis}</span>
      /&gt;
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;DataSourcesSection /&gt;</span>
    &lt;/div&gt;
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:00:40.781Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    